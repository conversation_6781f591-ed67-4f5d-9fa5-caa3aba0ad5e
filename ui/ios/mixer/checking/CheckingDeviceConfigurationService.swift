//
//  CheckingDeviceConfigurationService.swift
//  Cap-iOS
//
//  Created by <PERSON> on 19/06/2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

typealias CheckingDeviceContinueProcess = Bool

@MainActor
class CheckingDeviceConfigurationService {

    var uploadSpeed: UploadSpeedService

    init(uploadSpeed: UploadSpeedService) {
        self.uploadSpeed = uploadSpeed
    }

    lazy var availableStorage: Int64? = {
        return DeviceStorageUtility.getDeviceFreeSpace()
    }()

    lazy var currentProfile: SwitcherStreamSettings? = {
        let lib = BCProfileLibrary.shared
        let currentProfileType = lib.currentProfileType
        return lib.profile(withId: lib.currentProfileId)?.cloneWithDefaultValues()
    }()

    var recommendQuality: SourceConf.Size? {
        return uploadSpeed.recommendedQuality
    }

    var maxBitRate: Int? {
        if let uploadSpeedBitRate = uploadSpeed.result {
            return Int(uploadSpeedBitRate * 1024 * 1024 / 2.0)
        }
        return nil
    }

    var currentProfileType: BCProfileType {
        let lib = BCProfileLibrary.shared
        return lib.currentProfileType
    }

    var showWarningStreamQuality: Bool {
        if currentProfileType == .multiStreaming,
           isLowerStreamQualityRecommended,
           recommendQuality != nil {
            return true
        }
        return false
    }

    var showWarningBitRateSuggestion: Bool {
        if currentProfileType == .customRtmp,
            let maxBitRate = maxBitRate,
            let profile = currentProfile,
            let videoBitRate = profile.videoBitRate,
           let audioBitRate = profile.audioBitRate {
            let totalBitRate = videoBitRate + audioBitRate
            if totalBitRate > maxBitRate {
                return true
            }
        }
        return false
    }

    // Test if we should show the storage warning sheet
    var showWarningStorage: Bool {
        // an issue when compoute the available space ? we don't show the warning
        guard let availableSpace = availableStorage else {
            return false
        }

        // if there is no local recording, we don't show the warning
        if !BCProfileLibrary.shared.programRecording {
            return false
        }

        // we show the warning only for multistreaming and/or custom rtmp
        if BCProfileLibrary.shared.currentProfileType != .multiStreaming &&
            BCProfileLibrary.shared.currentProfileType != .customRtmp {
            return false
        }

        guard var profile = currentProfile else {
            return false
        }

        if BCProfileLibrary.shared.currentProfileType == .multiStreaming {
            // if multistreaming we get the videobitrate from stream quality in the output tab
            profile.videoFrameWidthNumber = NSNumber(value: BCProfileLibrary.shared.streamingFrameSize.width)
            profile.videoFrameHeightNumber = NSNumber(value: BCProfileLibrary.shared.streamingFrameSize.height)
            profile.videoBitRateNumber = nil // to recompute the video bitrate
            profile = profile.cloneWithDefaultValues()
        }

        guard let videoBitRate = profile.videoBitRate else {
            return false
        }

        // Estimate the size we need for 1 hour of recording
        let estimateSize = Int64((Double)(videoBitRate) * 3600 / 8)

        return estimateSize > availableSpace
                                 
    }

    var isLowerStreamQualityRecommended: Bool {
        if let recommendedHeight = uploadSpeed.recommendedQuality?.height {
            return BCProfileLibrary.shared.streamingFrameSize.height > recommendedHeight
        }
        return false
    }



    /// Get total device storage space in bytes
    var totalStorage: Int64? {
        return DeviceStorageUtility.getTotalDeviceSpace()
    }

}
