{"originHash": "0467ef3a63a876e0327fbe7fc820a44be4efa10cb9f0ef196a7fd79c02e46f3f", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "braze-swift-sdk", "kind": "remoteSourceControl", "location": "https://github.com/braze-inc/braze-swift-sdk", "state": {"revision": "a8ac3eb1055c79fa8bf55661b7dc07ae736b4f03", "version": "6.6.2"}}, {"identity": "clarity-apps", "kind": "remoteSourceControl", "location": "https://github.com/microsoft/clarity-apps", "state": {"revision": "49056c5b238930182d9309070d3f457d7d88a65b", "version": "3.0.7"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "fdc352fabaf5916e7faa1f96ad02b1957e93e5a5", "version": "11.15.0"}}, {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk", "state": {"revision": "973bd67929bfbe465f63c05cd5a4b113765fbc7f", "version": "2.2.1"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "45ce435e9406d3c674dd249a042b932bee006f60", "version": "11.15.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "c756a29784521063b6a1202907e2cc47f41b667c", "version": "4.5.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "lottie-ios", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-ios", "state": {"revision": "047aa81b77adcbf583a966dfef620d17650cc656", "version": "4.5.1"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "cac9a55a3ae92478a2c95042dcc8d9695d2129ca", "version": "5.21.0"}}, {"identity": "showtouches", "kind": "remoteSourceControl", "location": "https://github.com/Cyberbeni/ShowTouches", "state": {"revision": "1c32407861e20ee2b046e1119c36e9d6385b4ab0", "version": "1.1.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}}, {"identity": "tuskit", "kind": "remoteSourceControl", "location": "https://github.com/tus/TUSKit", "state": {"revision": "7363d0b95efdf97126030d8f187a371a9b11edc7", "version": "3.4.3"}}, {"identity": "zipfoundation", "kind": "remoteSourceControl", "location": "https://github.com/weichsel/ZIPFoundation", "state": {"revision": "02b6abe5f6eef7e3cbd5f247c5cc24e246efcfe0", "version": "0.9.19"}}], "version": 3}