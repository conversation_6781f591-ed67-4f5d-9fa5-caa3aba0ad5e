//
//  SourceCaptureUploadCoordinator.swift
//  Switcher-Pro
//
//  Created by AI Assistant on 05.09.25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
import UIKit

// MARK: - Upload Batch Management

/// Manages multiple source capture uploads using shared container credentials
actor SourceCaptureUploadCoordinator {
    
    // MARK: - Constants
    
    private static let MAX_CONCURRENT_UPLOADS = 3
    private static let FOLDER_DATE_FORMAT = "yyyy-MM-dd"
    
    // MARK: - Properties
    
    private var containerCredentials: ContainerCredentials?
    private var activeUploads: [String: SourceCaptureUploader] = [:]
    private var uploadQueue: [UploadRequest] = []
    private var isProcessingQueue = false
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = FOLDER_DATE_FORMAT
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        return formatter
    }()
    
    // MARK: - Data Models

    struct ContainerCredentialsResponse: Codable {
        let baseUrl: String
        let sasToken: String

        enum CodingKeys: String, CodingKey {
            case baseUrl = "BaseUrl"
            case sasToken = "SasToken"
        }
    }

    struct ContainerCredentials {
        let baseUrl: String
        let sasToken: String
        let expiresAt: Date

        var isExpired: Bool {
            return Date() >= expiresAt
        }

        func createBlobUrl(for filename: String, broadcastId: String, timestamp: Date) -> URL? {
            let dateString = DateFormatter().apply { formatter in
                formatter.dateFormat = SourceCaptureUploadCoordinator.FOLDER_DATE_FORMAT
                formatter.timeZone = TimeZone(abbreviation: "UTC")
            }.string(from: timestamp)

            let folderPath = "\(dateString)/\(broadcastId)/\(filename)"
            guard let baseURL = URL(string: baseUrl) else { return nil }

            // Build the URL with path, then append SAS token directly to avoid double encoding
            let pathURL = baseURL.appendingPathComponent(folderPath)
            let urlString = pathURL.absoluteString + "?" + sasToken

            return URL(string: urlString)
        }
    }
    
    struct UploadRequest {
        let id: String
        let captureImage: UIImage
        let broadcastId: String
        let channelId: String
        let sourceType: SourcePanelLogType
        let isActivation: Bool
        let ordinal: Int
        let timestamp: Date
        let completion: (Result<String, Error>) -> Void
    }
    
    // MARK: - Public Interface
    
    /// Queue a capture for upload
    func queueUpload(
        captureImage: UIImage,
        broadcastId: String,
        channelId: String,
        sourceType: SourcePanelLogType,
        isActivation: Bool,
        ordinal: Int,
        timestamp: Date = Date()
    ) async -> Result<String, Error> {
        
        return await withCheckedContinuation { continuation in
            let request = UploadRequest(
                id: UUID().uuidString,
                captureImage: captureImage,
                broadcastId: broadcastId,
                channelId: channelId,
                sourceType: sourceType,
                isActivation: isActivation,
                ordinal: ordinal,
                timestamp: timestamp
            ) { result in
                continuation.resume(returning: result)
            }
            
            uploadQueue.append(request)
            
            Task {
                await processQueue()
            }
        }
    }
    
    /// Cancel all pending uploads
    func cancelAllUploads() async {
        uploadQueue.removeAll()
        
        for uploader in activeUploads.values {
            await uploader.cancel()
        }
        
        activeUploads.removeAll()
        containerCredentials = nil
    }
    
    // MARK: - Private Methods
    
    private func processQueue() async {
        guard !isProcessingQueue else { return }
        guard !uploadQueue.isEmpty else { return }
        guard activeUploads.count < Self.MAX_CONCURRENT_UPLOADS else { return }
        
        isProcessingQueue = true
        defer { isProcessingQueue = false }
        
        while !uploadQueue.isEmpty && activeUploads.count < Self.MAX_CONCURRENT_UPLOADS {
            let request = uploadQueue.removeFirst()
            
            do {
                try await startUpload(request: request)
            } catch {
                request.completion(.failure(error))
            }
        }
    }
    
    private func startUpload(request: UploadRequest) async throws {
        // Check if we need fresh container credentials
        if containerCredentials?.isExpired != false {
            try await refreshContainerCredentials()
        }
        
        guard let credentials = containerCredentials else {
            throw SourceCaptureError.missingContainerCredentials
        }
        
        // Create the uploader with container credentials
        let delegate = UploadDelegate(
            requestId: request.id,
            coordinator: self,
            completion: request.completion
        )

        let uploader = SourceCaptureUploader(
            captureImage: request.captureImage,
            broadcastId: request.broadcastId,
            channelId: request.channelId,
            sourceType: request.sourceType,
            isActivation: request.isActivation,
            ordinal: request.ordinal,
            timestamp: request.timestamp,
            containerCredentials: credentials,
            delegate: delegate
        )

        activeUploads[request.id] = uploader
        await uploader.start()
    }
    
    private func refreshContainerCredentials() async throws {
        // Call the SourceLogUpload API to get fresh BaseUrl/SasToken
        // The API now returns container-scoped credentials that work for any broadcast

        guard let url = SSNUserAccount.shared.apiUrl(withSuffix: "api/Telemetry/SourcesLogUpload") else {
            throw SourceCaptureError.invalidResponse
        }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        if let accessToken = SSNUserAccount.shared.accessToken {
            request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        }

        // Empty request body since we're just requesting container credentials
        request.httpBody = try JSONSerialization.data(withJSONObject: [:])

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw SourceCaptureError.invalidResponse
        }

        let decoder = JSONDecoder()
        let apiResponse = try decoder.decode(ContainerCredentialsResponse.self, from: data)

        // Create credentials with <1 hour expiry (see API implementation)
        // Note: broadcastId will be passed when creating blob URLs
        containerCredentials = ContainerCredentials(
            baseUrl: apiResponse.baseUrl,
            sasToken: apiResponse.sasToken,
            expiresAt: Date().addingTimeInterval(3300) // 55 mins
        )
    }
    
    fileprivate func uploadCompleted(requestId: String) {
        activeUploads.removeValue(forKey: requestId)
        
        // Process more uploads if queue has items
        Task {
            await processQueue()
        }
    }
}

// MARK: - Upload Delegate

private class UploadDelegate: SourceCaptureUploaderDelegate {
    let requestId: String
    let coordinator: SourceCaptureUploadCoordinator
    let completion: (Result<String, Error>) -> Void
    
    init(requestId: String, coordinator: SourceCaptureUploadCoordinator, completion: @escaping (Result<String, Error>) -> Void) {
        self.requestId = requestId
        self.coordinator = coordinator
        self.completion = completion
    }
    
    func sourceCaptureUploaderDidFinish(_ uploader: any SourceCaptureUploaderProtocol, result: Result<String, any Error>) {
        completion(result)
        
        Task {
            await coordinator.uploadCompleted(requestId: requestId)
        }
    }
}

// MARK: - Extensions

extension DateFormatter {
    func apply(_ closure: (DateFormatter) -> Void) -> DateFormatter {
        closure(self)
        return self
    }
}


