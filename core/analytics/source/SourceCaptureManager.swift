//
//  SourceCaptureManager.swift
//  Switcher-Pro
//
//  Created by AI Assistant on 2025-01-15.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
import UIKit

/// Manages local capture storage during production and batch upload at the end
/// Performs all file I/O and image processing on background threads
public class SourceCaptureManager {
    
    // MARK: - Constants

    private static let maxCaptureStorageMB: Int64 = 50 // 50MB max storage
    private static let minFreeStoragePercent: Double = 0.10 // 10% minimum free storage
    private static let requiredUploadSpeedMbps: Double = 10.0 // 10Mbps minimum upload speed
    private static let defaultSamplingRate: Double = 0.1 // 10% default sampling rate

    // 50MB will transfer over 10Mbps in ~40 seconds

    // MARK: - Properties

    private let broadcastId: String
    private let baseDirectory: URL
    private var currentStorageUsed: Int64 = 0
    private var captureCount: Int = 0
    private var nextOrdinal: Int = 1
    private var samplingRate: Double = defaultSamplingRate

    // Track files currently being uploaded to prevent cleanup race conditions
    private var filesBeingUploaded: Set<String> = []
    private let uploadLock = NSLock()

    // Upload progress tracking
    private var uploadProgress: Double = 0.0
    private var uploadStartTime: Date?
    private var estimatedUploadDuration: TimeInterval = 0.0

    // Upload coordinator for batch uploads with shared credentials
    private lazy var uploadCoordinator = SourceCaptureUploadCoordinator()

    // Upload speed service for network checks
    private let uploadSpeedService: UploadSpeedService

    private let logger = LsLogger(subsystem: "swi.analytics", category: "SourceCaptureManager")
    
    // MARK: - Static Methods

    /// Clean up orphaned capture files on app startup
    public static func performStartupCleanup() {
        let logger = LsLogger(subsystem: "swi.analytics", category: "SourceCaptureManager")

        let documentsPath = FileManager.default.temporaryDirectory
        let capturesRoot = documentsPath.appendingPathComponent("SourceCaptures")

        guard FileManager.default.fileExists(atPath: capturesRoot.path) else {
            return // No captures directory exists
        }

        guard let dateFolders = try? FileManager.default.contentsOfDirectory(at: capturesRoot, includingPropertiesForKeys: [.creationDateKey]) else {
            return
        }

        let calendar = Calendar.current
        let cutoffDate = calendar.date(byAdding: .hour, value: -2, to: Date()) ?? Date() // Only clean up folders older than 2 hours to avoid active uploads

        var cleanedCount = 0
        var totalSize: Int64 = 0

        for dateFolder in dateFolders {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: dateFolder.path)
                if let creationDate = attributes[.creationDate] as? Date,
                   creationDate < cutoffDate {

                    // Calculate size before deletion
                    if let folderSize = try? getFolderSize(url: dateFolder) {
                        totalSize += folderSize
                    }

                    try FileManager.default.removeItem(at: dateFolder)
                    cleanedCount += 1
                    logger.info("Startup cleanup: removed old capture folder \(dateFolder.lastPathComponent)")
                }
            } catch {
                logger.warning("Startup cleanup failed for folder \(dateFolder.lastPathComponent): \(error)")
            }
        }

        if cleanedCount > 0 {
            let sizeMB = Double(totalSize) / (1024 * 1024)
            logger.info("Startup cleanup completed: removed \(cleanedCount) folders, freed \(String(format: "%.1f", sizeMB)) MB")
        }
    }

    /// Calculate the total size of a folder
    private static func getFolderSize(url: URL) throws -> Int64 {
        let files = try FileManager.default.contentsOfDirectory(at: url, includingPropertiesForKeys: [.fileSizeKey])
        var totalSize: Int64 = 0

        for file in files {
            let attributes = try FileManager.default.attributesOfItem(atPath: file.path)
            if let fileSize = attributes[.size] as? NSNumber {
                totalSize += fileSize.int64Value
            }
        }

        return totalSize
    }



    // MARK: - Initialization

    init(broadcastId: String, uploadSpeedService: UploadSpeedService) {
        self.broadcastId = broadcastId
        self.uploadSpeedService = uploadSpeedService

        // Create directory structure: Documents/SourceCaptures/yyyy-MM-dd/broadcastId/
        let documentsPath = FileManager.default.temporaryDirectory
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: Date())

        self.baseDirectory = documentsPath
            .appendingPathComponent("SourceCaptures")
            .appendingPathComponent(dateString)
            .appendingPathComponent(broadcastId)

        // Create directory if it doesn't exist
        try? FileManager.default.createDirectory(at: baseDirectory, withIntermediateDirectories: true)

        // Calculate current storage used
        calculateCurrentStorageUsed()

        // Fetch sampling rate from CloudSettings (async, but store for sync access)
        fetchSamplingRateFromSettings()
    }
    
    // MARK: - Public Methods
    
    /// Check if captures should be taken based on feature flag, sampling rate and conditions
    func shouldCaptureImages() -> Bool {
        // Check feature flag first
        guard isSourceLogsFeatureEnabled() else {
            logger.info("Skipping capture - sourcelogs feature flag disabled")
            return false
        }

        // Check sampling rate from settings
        let shouldSample = Double.random(in: 0...1) < samplingRate
        guard shouldSample else {
            logger.info("Skipping capture due to sampling rate (\(samplingRate))")
            return false
        }
        
        // Check network conditions
        guard isNetworkSuitableForUpload() else {
            logger.info("Skipping capture due to network conditions")
            return false
        }
        
        // Check storage conditions
        guard hasEnoughStorage() else {
            logger.info("Skipping capture due to storage constraints")
            return false
        }
        
        return true
    }
    
    /// Save capture to local storage during production (throws errors for proper handling)
    /// Performs image processing and file I/O on background thread
    func saveCaptureSafely(_ image: UIImage, sourceId: String, sourceType: SourcePanelLogType, isActivation: Bool) async throws -> String? {
        return try await Task.detached(priority: .utility) {
            return try self.saveCaptureInternal(image, sourceId: sourceId, sourceType: sourceType, isActivation: isActivation)
        }.value
    }


    /// Internal implementation of capture saving
    private func saveCaptureInternal(_ image: UIImage, sourceId: String, sourceType: SourcePanelLogType, isActivation: Bool) throws -> String? {
        // Check if we can save more captures
        guard canSaveMoreCaptures() else {
            let error = NSError(domain: "SourceCaptureManager", code: 1001,
                               userInfo: [NSLocalizedDescriptionKey: "Storage limit reached - cannot save more captures"])
            throw error
        }

        // Generate filename with ordinal
        let currentOrdinal = nextOrdinal
        nextOrdinal += 1

        let actionType = isActivation ? "activation" : "deactivation"
        let filename = "\(currentOrdinal)_\(sourceType.id)_\(actionType)_\(sourceId).jpg"
        let fileURL = baseDirectory.appendingPathComponent(filename)

        // Convert to JPEG and save
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            let error = NSError(domain: "SourceCaptureManager", code: 1002,
                               userInfo: [NSLocalizedDescriptionKey: "Failed to convert image to JPEG data"])
            throw error
        }

        do {
            try imageData.write(to: fileURL)

            // Update storage tracking
            currentStorageUsed += Int64(imageData.count)
            captureCount += 1

            logger.info("Saved capture: \(filename) (\(imageData.count) bytes)")
            return filename

        } catch {
            // Re-throw with more context
            let contextError = NSError(domain: "SourceCaptureManager", code: 1003,
                                     userInfo: [NSLocalizedDescriptionKey: "Failed to write capture file: \(error.localizedDescription)",
                                               NSUnderlyingErrorKey: error])
            throw contextError
        }
    }
    
    /// Upload all captures to Azure at the end of production
    /// Performs all network operations on background thread
    func uploadCaptures(progressCallback: @escaping (Double) -> Void) async throws {
        let captureFiles = getCaptureFiles()

        guard !captureFiles.isEmpty else {
            logger.info("No captures to upload")
            await MainActor.run { progressCallback(1.0) }
            return
        }

        // Start tracking upload progress
        startUploadTracking()

        logger.info("Starting batch upload of \(captureFiles.count) captures using coordinator")

        var uploadedCount = 0
        var failedCount = 0
        let totalCount = captureFiles.count

        // Track which files were successfully uploaded for cleanup
        var uploadedFiles: [URL] = []

        // Process uploads concurrently using the coordinator
        await withTaskGroup(of: (URL, Result<String, Error>).self) { group in
            for fileURL in captureFiles {
                group.addTask {
                    let filename = fileURL.lastPathComponent

                    // Mark file as being uploaded
                    self.uploadLock.lock()
                    self.filesBeingUploaded.insert(filename)
                    self.uploadLock.unlock()

                    let result = await self.uploadSingleCaptureWithCoordinator(fileURL: fileURL)

                    // Remove file from upload tracking
                    self.uploadLock.lock()
                    self.filesBeingUploaded.remove(filename)
                    self.uploadLock.unlock()

                    return (fileURL, result)
                }
            }

            // Collect results as they complete
            for await (fileURL, result) in group {
                switch result {
                case .success:
                    uploadedCount += 1
                    uploadedFiles.append(fileURL)
                    logger.info("Uploaded capture \(uploadedCount)/\(totalCount)")

                case .failure(let error):
                    failedCount += 1
                    let filename = fileURL.lastPathComponent
                    logger.warning("Non-critical capture upload failed - file: \(filename), broadcast: \(broadcastId): \(error.localizedDescription)")
                }

                let progress = Double(uploadedCount + failedCount) / Double(totalCount)
                updateUploadProgress(progress)
                await MainActor.run { progressCallback(progress) }
            }
        }

        logger.info("Completed batch capture upload: \(uploadedCount)/\(totalCount) successful, \(failedCount) failed")

        // Clean up successfully uploaded files immediately
        for uploadedFile in uploadedFiles {
            let filename = uploadedFile.lastPathComponent

            // Double-check file is not being uploaded by another process
            uploadLock.lock()
            let isBeingUploaded = filesBeingUploaded.contains(filename)
            uploadLock.unlock()

            if !isBeingUploaded {
                do {
                    try FileManager.default.removeItem(at: uploadedFile)
                    logger.debug(level: 1, "Cleaned up uploaded file: \(filename)")
                } catch {
                    logger.warning("Failed to cleanup uploaded file \(filename): \(error)")
                }
            } else {
                logger.warning("Skipping cleanup of file still being uploaded: \(filename)")
            }
        }

        // If all uploads failed, still clean up after a delay to prevent storage buildup
        if uploadedCount == 0 && failedCount > 0 {
            logger.warning("All capture uploads failed - will cleanup files after delay")
            DispatchQueue.global().asyncAfter(deadline: .now() + 300) { // 5 minute delay
                self.cleanupCurrentBroadcast()
            }
        }
    }
    
    /// Clean up local capture files after upload or on failure
    func cleanup() {
        cleanupCurrentBroadcast()
        cleanupOldCaptures()
    }

    /// Clean up captures for the current broadcast
    private func cleanupCurrentBroadcast() {
        do {
            try FileManager.default.removeItem(at: baseDirectory)
            logger.info("Cleaned up capture directory for broadcast: \(broadcastId)")
        } catch {
            logger.error("Failed to cleanup capture directory: \(error)")
        }
    }

    /// Clean up old capture directories to prevent storage buildup
    private func cleanupOldCaptures() {
        let documentsPath = FileManager.default.temporaryDirectory
        let capturesRoot = documentsPath.appendingPathComponent("SourceCaptures")

        guard let dateFolders = try? FileManager.default.contentsOfDirectory(at: capturesRoot, includingPropertiesForKeys: [.creationDateKey]) else {
            return
        }

        let calendar = Calendar.current
        let cutoffDate = calendar.date(byAdding: .day, value: -7, to: Date()) ?? Date() // Keep only last 7 days

        for dateFolder in dateFolders {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: dateFolder.path)
                if let creationDate = attributes[.creationDate] as? Date,
                   creationDate < cutoffDate {
                    try FileManager.default.removeItem(at: dateFolder)
                    logger.info("Cleaned up old capture folder: \(dateFolder.lastPathComponent)")
                }
            } catch {
                logger.warning("Failed to cleanup old capture folder \(dateFolder.lastPathComponent): \(error)")
            }
        }
    }

    // MARK: - Private Methods

    private func isSourceLogsFeatureEnabled() -> Bool {
        // Always enabled in dev profile for testing
        if ConfigurationDevelopmentManager.shared.isDevProfile {
            return true
        }

        // Check if user has the sourcelogs feature claim
        return SSNUserAccount.shared.userInfo?.isSourceLoggingEnabled ?? false
    }

    private func fetchSamplingRateFromSettings() {
        // For Dev profile, always use 100% sampling rate for testing
        if ConfigurationDevelopmentManager.shared.isDevProfile {
            samplingRate = 1.0
            return
        }

        // Fetch sampling rate from CloudSettings asynchronously and store it
        CloudSettings.shared.fetchSourceLogsSamplingRate { [weak self] fetchedSamplingRate in
            DispatchQueue.main.async {
                self?.samplingRate = fetchedSamplingRate
                self?.logger.info("Updated sampling rate from CloudSettings: \(fetchedSamplingRate)")
            }
        }
    }

    private func isNetworkSuitableForUpload() -> Bool {
        // For Dev profile, always allow captures for testing
        if ConfigurationDevelopmentManager.shared.isDevProfile {
            return true
        }

        // Check if on mobile data or low data mode
        let networkPath = NetworkInterfacesMonitor.shared.networkPath

        if networkPath.isConstrained {
            return false // Low data mode
        }

        if networkPath.usesInterfaceType(.cellular) {
            return false // Mobile data
        }

        // Check upload speed
        if let uploadSpeed = uploadSpeedService.result {
            return uploadSpeed >= Self.requiredUploadSpeedMbps
        }

        return false // Default to false if speed test not available, may revisit
    }

    private func hasEnoughStorage() -> Bool {
        guard let freeSpace = getDeviceFreeSpace(),
              let totalSpace = getTotalDeviceSpace() else {
            return false
        }

        let freeSpacePercent = Double(freeSpace) / Double(totalSpace)
        return freeSpacePercent >= Self.minFreeStoragePercent
    }

    private func canSaveMoreCaptures() -> Bool {
        let maxStorageBytes = Self.maxCaptureStorageMB * 1024 * 1024
        return currentStorageUsed < maxStorageBytes
    }

    private func getDeviceFreeSpace() -> Int64? {
        let documentDirectory = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).last!
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: documentDirectory),
              let freeSize = systemAttributes[.systemFreeSize] as? NSNumber else {
            return nil
        }
        return freeSize.int64Value
    }

    private func getTotalDeviceSpace() -> Int64? {
        let documentDirectory = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).last!
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: documentDirectory),
              let totalSize = systemAttributes[.systemSize] as? NSNumber else {
            return nil
        }
        return totalSize.int64Value
    }





    private func calculateCurrentStorageUsed() {
        let captureFiles = getCaptureFiles()
        currentStorageUsed = 0

        for fileURL in captureFiles {
            if let attributes = try? FileManager.default.attributesOfItem(atPath: fileURL.path),
               let fileSize = attributes[.size] as? NSNumber {
                currentStorageUsed += fileSize.int64Value
            }
        }

        captureCount = captureFiles.count
        logger.info("Current capture storage: \(currentStorageUsed) bytes, \(captureCount) files")
    }

    /// Get list of capture files ready for upload
    func getCaptureFiles() -> [URL] {
        guard let files = try? FileManager.default.contentsOfDirectory(at: baseDirectory, includingPropertiesForKeys: nil) else {
            return []
        }

        return files.filter { $0.pathExtension.lowercased() == "jpg" }
    }

    // MARK: - New Coordinator-based Upload Methods

    private func uploadSingleCaptureWithCoordinator(fileURL: URL) async -> Result<String, Error> {
        do {
            // Check if file still exists before attempting to read it
            guard FileManager.default.fileExists(atPath: fileURL.path) else {
                logger.warning("Capture file no longer exists, skipping upload: \(fileURL.lastPathComponent)")
                return .success("skipped") // Gracefully skip missing files
            }

            // Load image from file
            guard let imageData = try? Data(contentsOf: fileURL),
                  let image = UIImage(data: imageData) else {
                throw NSError(domain: "SourceCaptureManager", code: 1001,
                             userInfo: [NSLocalizedDescriptionKey: "Failed to load image from file: \(fileURL.lastPathComponent)"])
            }

            // Parse filename to extract metadata
            let filename = fileURL.lastPathComponent
            let components = filename.replacingOccurrences(of: ".jpg", with: "").components(separatedBy: "_")

            guard components.count == 4 else {
                throw NSError(domain: "SourceCaptureManager", code: 2,
                             userInfo: [NSLocalizedDescriptionKey: "Invalid filename format: \(filename)"])
            }

            let ordinal = Int(components[0]) ?? 0
            let sourceType = components[1]
            let actionType = components[2]
            let sourceId = components[3]

            // Convert sourceType string back to SourcePanelLogType
            guard let logType = SourcePanelLogType.from(id: sourceType) else {
                throw NSError(domain: "SourceCaptureManager", code: 3,
                             userInfo: [NSLocalizedDescriptionKey: "Unknown source type: \(sourceType)"])
            }

            let isActivation = actionType == "activation"

            // Use the upload coordinator for batch upload with shared credentials
            let result = await uploadCoordinator.queueUpload(
                captureImage: image,
                broadcastId: broadcastId,
                channelId: sourceId,
                sourceType: logType,
                isActivation: isActivation,
                ordinal: ordinal,
                timestamp: Date() // Use current time for upload
            )

            return result

        } catch {
            return .failure(error)
        }
    }



    // MARK: - Upload Progress and Time Estimation

    /// Estimate the time needed to upload captures in microseconds
    func estimateUploadTime() -> Int64 {
        // Only add capture upload time if we're actually uploading captures
        guard !getCaptureFiles().isEmpty else {
            return 0
        }

        // If upload is in progress, calculate remaining time based on actual progress
        if let startTime = uploadStartTime {
            let elapsedTime = Date().timeIntervalSince(startTime)
            let remainingProgress = 1.0 - uploadProgress

            if uploadProgress > 0.1 { // Only use dynamic calculation if we have meaningful progress
                let estimatedTotalTime = elapsedTime / uploadProgress
                let remainingTime = estimatedTotalTime * remainingProgress
                return Int64(max(remainingTime, 0) * 1_000_000) // Convert to microseconds
            }
        }

        // Initial estimate based on file count and network speed
        let captureCount = getCaptureFiles().count
        guard captureCount > 0 else { return 0 }

        let averageCaptureSizeKB: Double = 50 // ~50KB per capture
        let totalSizeKB = Double(captureCount) * averageCaptureSizeKB

        // Estimate upload speed (conservative estimate for mobile networks)
        let estimatedUploadSpeedKbps: Double = 1000 // 1 Mbps conservative estimate
        let estimatedUploadSpeedKBps = estimatedUploadSpeedKbps / 8 // Convert to KB/s

        let estimatedTimeSeconds = totalSizeKB / estimatedUploadSpeedKBps
        let bufferMultiplier = 1.5 // Add 50% buffer for API calls and processing

        return Int64(estimatedTimeSeconds * bufferMultiplier * 1_000_000) // Convert to microseconds
    }

    /// Reset upload tracking (called when starting a new production)
    func resetUploadTracking() {
        uploadProgress = 0.0
        uploadStartTime = nil
        estimatedUploadDuration = 0.0
    }

    /// Cancel all pending uploads
    func cancelAllUploads() async {
        await uploadCoordinator.cancelAllUploads()

        // Clear tracking
        uploadLock.lock()
        filesBeingUploaded.removeAll()
        uploadLock.unlock()

        resetUploadTracking()
    }

    /// Start tracking upload progress
    func startUploadTracking() {
        uploadStartTime = Date()
        uploadProgress = 0.0
    }

    /// Update upload progress (0.0 to 1.0)
    func updateUploadProgress(_ progress: Double) {
        uploadProgress = progress

        // Reset tracking when upload completes
        if progress >= 1.0 {
            uploadStartTime = nil
            uploadProgress = 0.0
        }
    }
}
