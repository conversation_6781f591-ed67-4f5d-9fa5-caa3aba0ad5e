//
//  SourcePanelLogEvent.swift
//  Cap-iOS
//
//  Created by <PERSON> on 7/25/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

enum SourcePanelLogEvent: AnalyticEvent {
    // MARK: - Cases
    case assetLogs(broadcastId: String, logs: [SourcePanelLog])

    // MARK: - Properties
    var name: String { "Source Panel Log" }

    var properties: [String: Any]? {
        switch self {
        case .assetLogs(let broadcastId, let logs):
            let logsArray: [[String: Any]] = logs.map { $0.toDictionary() }
            return [
                "broadcastId": broadcastId,
                "logs": logsArray
            ]
        }
    }
}
