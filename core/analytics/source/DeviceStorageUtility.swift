//
//  DeviceStorageUtility.swift
//  Cap-iOS
//
//  Created by Augment Agent on 2025-09-08.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

/// Utility class for device storage operations that can be used from any actor context
class DeviceStorageUtility {
    
    /// Get available free space on device in bytes
    static func getDeviceFreeSpace() -> Int64? {
        let documentDirectory = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).last!
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: documentDirectory),
              let freeSize = systemAttributes[.systemFreeSize] as? NSNumber else {
            return nil
        }
        return freeSize.int64Value
    }
    
    /// Get total device storage space in bytes
    static func getTotalDeviceSpace() -> Int64? {
        let documentDirectory = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).last!
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: documentDirectory),
              let totalSize = systemAttributes[.systemSize] as? NSNumber else {
            return nil
        }
        return totalSize.int64Value
    }
    
    /// Calculate free space as a percentage of total space
    static func getFreeSpacePercentage() -> Double? {
        guard let freeSpace = getDeviceFreeSpace(),
              let totalSpace = getTotalDeviceSpace(),
              totalSpace > 0 else {
            return nil
        }
        return Double(freeSpace) / Double(totalSpace)
    }
}
